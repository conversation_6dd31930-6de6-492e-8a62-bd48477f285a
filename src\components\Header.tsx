'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  const closeMobileMenu = () => {
    setMobileMenuOpen(false)
  }

  // Close mobile menu when clicking outside or on escape
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (mobileMenuOpen && !target.closest('nav')) {
        closeMobileMenu()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && mobileMenuOpen) {
        closeMobileMenu()
      }
    }

    if (mobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = ''
    }
  }, [mobileMenuOpen])

  const openContactModal = () => {
    const modal = document.getElementById('contactModal')
    if (modal) {
      modal.classList.add('active')
      document.body.style.overflow = 'hidden'
    }
    closeMobileMenu() // Close mobile menu when opening contact modal
  }

  return (
    <>
      <header>
        <nav className="container">
          <Link href="#hero" className="logo">
            DENIS
          </Link>

          <ul
            className={`nav-links ${mobileMenuOpen ? 'active' : ''}`}
            id="navMenu"
          >
            <li>
              <Link href="#hero" onClick={closeMobileMenu}>
                Home
              </Link>
            </li>
            <li>
              <Link href="#insights" onClick={closeMobileMenu}>
                Services
              </Link>
            </li>
            <li>
              <Link href="#case-study" onClick={closeMobileMenu}>
                Portfolio
              </Link>
            </li>
            <li>
              <Link href="#testimonials" onClick={closeMobileMenu}>
                Reviews
              </Link>
            </li>
            <li>
              <Link href="#faq" onClick={closeMobileMenu}>
                FAQ
              </Link>
            </li>
          </ul>

          <div className="nav-cta">
            <button
              className="btn btn-primary"
              onClick={openContactModal}
              id="getInTouchNavBtn"
            >
              Get In Touch
            </button>
          </div>

          <button
            className={`mobile-menu ${mobileMenuOpen ? 'active' : ''}`}
            id="mobileMenu"
            onClick={toggleMobileMenu}
            aria-label="Toggle mobile menu"
          >
            <span></span>
            <span></span>
            <span></span>
          </button>
        </nav>
      </header>

      {/* Mobile menu overlay */}
      <div
        className={`mobile-menu-overlay ${mobileMenuOpen ? 'active' : ''}`}
        onClick={closeMobileMenu}
      />
    </>
  )
}
