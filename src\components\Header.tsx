'use client'

import { useState } from 'react'
import Link from 'next/link'

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  const openContactModal = () => {
    const modal = document.getElementById('contactModal')
    if (modal) {
      modal.classList.add('active')
      document.body.style.overflow = 'hidden'
    }
  }

  return (
    <header>
      <nav className="container">
        <Link href="#hero" className="logo">
          DENIS ERASTUS
        </Link>

        <ul
          className={`nav-links ${mobileMenuOpen ? 'active' : ''}`}
          id="navMenu"
        >
          <li>
            <Link href="#hero">Home</Link>
          </li>
          <li>
            <Link href="#insights">Services</Link>
          </li>
          <li>
            <Link href="#case-study">Portfolio</Link>
          </li>
          <li>
            <Link href="#testimonials">Reviews</Link>
          </li>
          <li>
            <Link href="#faq">FAQ</Link>
          </li>
        </ul>

        <div className="nav-cta">
          <button
            className="btn btn-primary"
            onClick={openContactModal}
            id="getInTouchNavBtn"
          >
            Get In Touch
          </button>
        </div>

        <button
          className="mobile-menu"
          id="mobileMenu"
          onClick={toggleMobileMenu}
        >
          <span></span>
          <span></span>
          <span></span>
        </button>
      </nav>
    </header>
  )
}
